<<<<<<< HEAD
# Blog Serverless Backend

A serverless blog backend built with Node.js and PostgreSQL for deployment on Netlify.

## Features

- ✅ Complete CRUD operations for blog posts
- ✅ PostgreSQL database integration
- ✅ UUID-based IDs
- ✅ Automatic slug generation
- ✅ View and love counters
- ✅ Tag support
- ✅ Pagination support
- ✅ CORS enabled

## Blog Model

```javascript
{
  id: "UUID",
  title: "string",
  slug: "string",
  contentJson: "string",
  coverImageUrl: "string",
  shortDescription: "string", 
  tags: ["array", "of", "strings"],
  dateCreated: "DateTime",
  dateUpdated: "DateTime",
  loves: "number",
  views: "number"
}
```

## API Endpoints

### Get All Blogs
```
GET /.netlify/functions/blogs?limit=10&offset=0
```

### Get Single Blog
```
GET /.netlify/functions/blog-get?id=UUID
GET /.netlify/functions/blog-get?slug=blog-slug
```

### Create Blog
```
POST /.netlify/functions/blog-create
Content-Type: application/json

{
  "title": "Blog Title",
  "contentJson": "{}",
  "coverImageUrl": "https://example.com/image.jpg",
  "shortDescription": "Brief description",
  "tags": ["tag1", "tag2"]
}
```

### Update Blog
```
PUT /.netlify/functions/blog-update?id=UUID
Content-Type: application/json

{
  "title": "Updated Title",
  "contentJson": "{}",
  "coverImageUrl": "https://example.com/new-image.jpg",
  "shortDescription": "Updated description",
  "tags": ["new-tag"]
}
```

### Delete Blog
```
DELETE /.netlify/functions/blog-delete?id=UUID
```

### Increment Loves
```
POST /.netlify/functions/blog-love?id=UUID
```

## Setup

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
```

3. Configure your PostgreSQL database URL in `.env`:
```
DATABASE_URL=postgresql://username:password@hostname:port/database_name
```

4. Run locally:
```bash
npm run dev
```

5. Deploy to Netlify:
```bash
npm run deploy
```

## Environment Variables

Set these in your Netlify environment:

- `DATABASE_URL`: Your PostgreSQL connection string
- `NODE_ENV`: Set to "production" for production deployment

## Database Setup

The database table will be created automatically when you first run any endpoint. The system creates:

- `blogs` table with all required columns
- Indexes for performance (slug, date_created)
- UUID primary keys
- Array support for tags

## CORS

All endpoints include CORS headers to allow frontend access from any domain.
=======
# PortfolioNodeJs

This repository was initialized by Builder.io.

## Getting Started

Welcome to your new repository! You can now start building your project.
>>>>>>> origin/main
