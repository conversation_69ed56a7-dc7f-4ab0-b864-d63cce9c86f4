require('dotenv').config();
const http = require('http');
const url = require('url');
const path = require('path');
const fs = require('fs');

// Import functions
const blogCreate = require('./netlify/functions/blog-create');
const blogGet = require('./netlify/functions/blog-get');
const blogUpdate = require('./netlify/functions/blog-update');
const blogDelete = require('./netlify/functions/blog-delete');
const blogLove = require('./netlify/functions/blog-love');
const blogs = require('./netlify/functions/blogs');

const PORT = process.env.PORT || 8888;

// Helper function to handle Netlify functions
const handleFunction = async (handler, req, res) => {
  // Parse query parameters
  const parsedUrl = url.parse(req.url, true);

  // Parse body for POST/PUT requests
  let body = '';
  if (req.method === 'POST' || req.method === 'PUT') {
    await new Promise((resolve) => {
      req.on('data', chunk => {
        body += chunk.toString();
      });
      req.on('end', resolve);
    });
  }

  const event = {
    httpMethod: req.method,
    queryStringParameters: parsedUrl.query,
    body: body,
    headers: req.headers
  };

  const context = {};

  try {
    const result = await handler.handler(event, context);

    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Content-Type', 'application/json');

    if (result.headers) {
      Object.keys(result.headers).forEach(key => {
        res.setHeader(key, result.headers[key]);
      });
    }

    res.statusCode = result.statusCode;
    res.end(result.body);
  } catch (error) {
    console.error('Function error:', error);
    res.statusCode = 500;
    res.end(JSON.stringify({ error: 'Internal server error' }));
  }
};

// Create HTTP server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.statusCode = 200;
    res.end();
    return;
  }

  // Route to functions
  if (pathname === '/.netlify/functions/blog-create') {
    await handleFunction(blogCreate, req, res);
  } else if (pathname === '/.netlify/functions/blog-get') {
    await handleFunction(blogGet, req, res);
  } else if (pathname === '/.netlify/functions/blog-update') {
    await handleFunction(blogUpdate, req, res);
  } else if (pathname === '/.netlify/functions/blog-delete') {
    await handleFunction(blogDelete, req, res);
  } else if (pathname === '/.netlify/functions/blog-love') {
    await handleFunction(blogLove, req, res);
  } else if (pathname === '/.netlify/functions/blogs') {
    await handleFunction(blogs, req, res);
  } else if (pathname === '/' || pathname === '/index.html') {
    // Serve index.html
    try {
      const indexPath = path.join(__dirname, 'public', 'index.html');
      const content = fs.readFileSync(indexPath, 'utf8');
      res.setHeader('Content-Type', 'text/html');
      res.statusCode = 200;
      res.end(content);
    } catch (error) {
      res.statusCode = 404;
      res.end('Index file not found');
    }
  } else {
    // Serve static files
    try {
      const filePath = path.join(__dirname, 'public', pathname);
      const content = fs.readFileSync(filePath);
      const ext = path.extname(filePath);

      if (ext === '.css') {
        res.setHeader('Content-Type', 'text/css');
      } else if (ext === '.js') {
        res.setHeader('Content-Type', 'application/javascript');
      } else if (ext === '.html') {
        res.setHeader('Content-Type', 'text/html');
      }

      res.statusCode = 200;
      res.end(content);
    } catch (error) {
      res.statusCode = 404;
      res.end('File not found');
    }
  }
});

server.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📝 API endpoints available at http://localhost:${PORT}/.netlify/functions/`);
  console.log(`🌐 Frontend available at http://localhost:${PORT}`);
});
