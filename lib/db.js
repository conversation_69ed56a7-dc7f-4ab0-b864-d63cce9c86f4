const { Pool } = require('pg');

let pool;

const getPool = () => {
  if (!pool) {
    pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: { rejectUnauthorized: false },
      max: 3,
      idleTimeoutMillis: 10000,
      connectionTimeoutMillis: 5000,
      acquireTimeoutMillis: 5000,
      statement_timeout: 5000,
      query_timeout: 5000,
    });

    pool.on('error', (err) => {
      console.error('Unexpected error on idle client', err);
    });

    pool.on('connect', () => {
      console.log('✅ Database connected successfully');
    });
  }
  return pool;
};

const query = async (text, params) => {
  let client;
  try {
    client = await getPool().connect();
    const result = await client.query(text, params);
    return result;
  } catch (error) {
    console.error('Database query error:', error.message);
    throw error;
  } finally {
    if (client) {
      client.release();
    }
  }
};

module.exports = { query, getPool };
