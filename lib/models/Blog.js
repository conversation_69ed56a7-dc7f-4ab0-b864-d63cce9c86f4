const { query } = require('../db');
const { v4: uuidv4 } = require('uuid');
const slugify = require('slugify');

class Blog {
  constructor(data) {
    this.id = data.id || uuidv4();
    this.title = data.title;
    this.slug = data.slug || this.generateSlug(data.title);
    this.contentJson = data.contentJson;
    this.coverImageUrl = data.coverImageUrl;
    this.shortDescription = data.shortDescription;
    this.tags = Array.isArray(data.tags) ? data.tags : [];
    this.dateCreated = data.dateCreated || new Date();
    this.dateUpdated = data.dateUpdated || null;
    this.loves = data.loves || 0;
    this.views = data.views || 0;
  }

  generateSlug(title) {
    return slugify(title, { lower: true, strict: true });
  }

  static async createTable() {
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS blogs (
        id UUID PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        content_json TEXT,
        cover_image_url TEXT,
        short_description TEXT,
        tags TEXT[],
        date_created TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        date_updated TIMESTAMP WITH TIME ZONE,
        loves INTEGER DEFAULT 0,
        views INTEGER DEFAULT 0
      );
      
      CREATE INDEX IF NOT EXISTS idx_blogs_slug ON blogs(slug);
      CREATE INDEX IF NOT EXISTS idx_blogs_date_created ON blogs(date_created DESC);
    `;
    
    await query(createTableQuery);
  }

  async save() {
    const insertQuery = `
      INSERT INTO blogs (id, title, slug, content_json, cover_image_url, short_description, tags, date_created, loves, views)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `;
    
    const values = [
      this.id,
      this.title,
      this.slug,
      this.contentJson,
      this.coverImageUrl,
      this.shortDescription,
      this.tags,
      this.dateCreated,
      this.loves,
      this.views
    ];

    const result = await query(insertQuery, values);
    return result.rows[0];
  }

  async update() {
    this.dateUpdated = new Date();
    
    const updateQuery = `
      UPDATE blogs 
      SET title = $2, slug = $3, content_json = $4, cover_image_url = $5, 
          short_description = $6, tags = $7, date_updated = $8, loves = $9, views = $10
      WHERE id = $1
      RETURNING *
    `;
    
    const values = [
      this.id,
      this.title,
      this.slug,
      this.contentJson,
      this.coverImageUrl,
      this.shortDescription,
      this.tags,
      this.dateUpdated,
      this.loves,
      this.views
    ];

    const result = await query(updateQuery, values);
    return result.rows[0];
  }

  static async findById(id) {
    const selectQuery = 'SELECT * FROM blogs WHERE id = $1';
    const result = await query(selectQuery, [id]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return new Blog({
      id: result.rows[0].id,
      title: result.rows[0].title,
      slug: result.rows[0].slug,
      contentJson: result.rows[0].content_json,
      coverImageUrl: result.rows[0].cover_image_url,
      shortDescription: result.rows[0].short_description,
      tags: result.rows[0].tags || [],
      dateCreated: result.rows[0].date_created,
      dateUpdated: result.rows[0].date_updated,
      loves: result.rows[0].loves,
      views: result.rows[0].views
    });
  }

  static async findBySlug(slug) {
    const selectQuery = 'SELECT * FROM blogs WHERE slug = $1';
    const result = await query(selectQuery, [slug]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return new Blog({
      id: result.rows[0].id,
      title: result.rows[0].title,
      slug: result.rows[0].slug,
      contentJson: result.rows[0].content_json,
      coverImageUrl: result.rows[0].cover_image_url,
      shortDescription: result.rows[0].short_description,
      tags: result.rows[0].tags || [],
      dateCreated: result.rows[0].date_created,
      dateUpdated: result.rows[0].date_updated,
      loves: result.rows[0].loves,
      views: result.rows[0].views
    });
  }

  static async findAll(limit = 10, offset = 0) {
    const selectQuery = `
      SELECT * FROM blogs 
      ORDER BY date_created DESC 
      LIMIT $1 OFFSET $2
    `;
    const result = await query(selectQuery, [limit, offset]);
    
    return result.rows.map(row => new Blog({
      id: row.id,
      title: row.title,
      slug: row.slug,
      contentJson: row.content_json,
      coverImageUrl: row.cover_image_url,
      shortDescription: row.short_description,
      tags: row.tags || [],
      dateCreated: row.date_created,
      dateUpdated: row.date_updated,
      loves: row.loves,
      views: row.views
    }));
  }

  static async delete(id) {
    const deleteQuery = 'DELETE FROM blogs WHERE id = $1 RETURNING *';
    const result = await query(deleteQuery, [id]);
    return result.rows.length > 0;
  }

  async incrementViews() {
    this.views += 1;
    const updateQuery = 'UPDATE blogs SET views = views + 1 WHERE id = $1 RETURNING views';
    const result = await query(updateQuery, [this.id]);
    return result.rows[0].views;
  }

  async incrementLoves() {
    this.loves += 1;
    const updateQuery = 'UPDATE blogs SET loves = loves + 1 WHERE id = $1 RETURNING loves';
    const result = await query(updateQuery, [this.id]);
    return result.rows[0].loves;
  }
}

module.exports = Blog;
