{"name": "blog-serverless-backend", "version": "1.0.0", "description": "Serverless blog backend with PostgreSQL for Netlify", "main": "index.js", "scripts": {"dev": "netlify dev", "build": "echo 'Build successful'", "deploy": "netlify deploy --prod"}, "dependencies": {"pg": "^8.11.3", "uuid": "^9.0.1", "slugify": "^1.6.6"}, "devDependencies": {"netlify-cli": "^17.10.1"}, "keywords": ["serverless", "blog", "postgresql", "netlify", "crud"], "author": "", "license": "MIT"}