{"name": "blog-serverless-backend", "version": "1.0.0", "description": "Serverless blog backend with PostgreSQL for Netlify", "main": "index.js", "scripts": {"dev": "netlify dev", "start": "node simple-server.js", "local": "node simple-server.js", "build": "echo 'Build successful'", "deploy": "netlify deploy --prod"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "pg": "^8.11.3", "slugify": "^1.6.6", "uuid": "^9.0.1"}, "devDependencies": {"netlify-cli": "^17.10.1"}, "keywords": ["serverless", "blog", "postgresql", "netlify", "crud"], "author": "", "license": "MIT"}