# مشروع Portfolio Blog API

## كيفية تشغيل المشروع محلياً

### المتطلبات
- Node.js (الإصدار 14 أو أحدث)
- npm

### خطوات التشغيل

#### 1. تثبيت المكتبات
```bash
npm install
```

#### 2. تشغيل المشروع محلياً (بدون قاعدة بيانات)
```bash
npm run local
```
أو
```bash
node simple-server.js
```

#### 3. تشغيل المشروع مع Netlify (يحتاج قاعدة بيانات)
```bash
npm run dev
```

### الوصول للمشروع
بعد تشغيل المشروع، يمكنك الوصول إليه على:
- **الصفحة الرئيسية**: http://localhost:8888
- **API**: http://localhost:8888/.netlify/functions/

### API Endpoints المتاحة

#### جلب جميع المقالات
```
GET /.netlify/functions/blogs
```

#### جلب مقال واحد
```
GET /.netlify/functions/blog-get?id=1
GET /.netlify/functions/blog-get?slug=welcome-to-my-blog
```

#### إنشاء مقال جديد
```
POST /.netlify/functions/blog-create
Content-Type: application/json

{
  "title": "عنوان المقال",
  "content": "محتوى المقال",
  "author": "اسم الكاتب"
}
```

#### زيادة عدد الإعجابات
```
POST /.netlify/functions/blog-love
Content-Type: application/json

{
  "id": "1"
}
```

### ملاحظات مهمة

1. **السيرفر البسيط (`simple-server.js`)**:
   - يعمل بدون قاعدة بيانات
   - يستخدم بيانات تجريبية في الذاكرة
   - مناسب للتطوير والاختبار المحلي

2. **السيرفر الأصلي (`server.js`)**:
   - يحتاج اتصال بقاعدة بيانات PostgreSQL
   - يستخدم جميع الـ functions الأصلية
   - مناسب للإنتاج

3. **Netlify Dev**:
   - يحتاج تثبيت `netlify-cli`
   - يحتاج إعداد قاعدة البيانات
   - يحاكي بيئة Netlify

### استكشاف الأخطاء

#### إذا لم يعمل `npm run dev`:
```bash
npm install -g netlify-cli
netlify dev
```

#### إذا كانت هناك مشاكل مع قاعدة البيانات:
استخدم السيرفر البسيط:
```bash
npm run local
```

#### إذا كانت هناك مشاكل مع المكتبات:
```bash
rm -rf node_modules package-lock.json
npm install
```

### البيانات التجريبية
السيرفر البسيط يحتوي على مقالين تجريبيين:
- "مرحباً بك في مدونتي"
- "مقال تجريبي"

يمكنك إضافة مقالات جديدة باستخدام API أو تعديل الملف `simple-server.js`.

---

**ملاحظة**: هذا المشروع مصمم للعمل مع Netlify Functions، لكن السيرفر البسيط يسمح بالتطوير المحلي بسهولة.
