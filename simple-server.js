const http = require('http');
const url = require('url');
const path = require('path');
const fs = require('fs');

const PORT = process.env.PORT || 8888;

// Mock data for testing
let blogs = [
  {
    id: '1',
    title: 'مرحباً بك في مدونتي',
    content: 'هذا مثال على مقال في المدونة',
    slug: 'welcome-to-my-blog',
    author: 'وسيم',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    love_count: 5
  },
  {
    id: '2',
    title: 'مقال تجريبي',
    content: 'هذا مقال تجريبي لاختبار المدونة',
    slug: 'test-article',
    author: 'وسيم',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    love_count: 3
  }
];

// Create HTTP server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Content-Type', 'application/json');
  
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.statusCode = 200;
    res.end();
    return;
  }
  
  // Parse body for POST/PUT requests
  let body = '';
  if (req.method === 'POST' || req.method === 'PUT') {
    await new Promise((resolve) => {
      req.on('data', chunk => {
        body += chunk.toString();
      });
      req.on('end', resolve);
    });
  }
  
  try {
    // Route to functions
    if (pathname === '/.netlify/functions/blogs') {
      // Get all blogs
      const page = parseInt(parsedUrl.query.page) || 1;
      const limit = parseInt(parsedUrl.query.limit) || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      
      const paginatedBlogs = blogs.slice(startIndex, endIndex);
      
      res.statusCode = 200;
      res.end(JSON.stringify({
        success: true,
        data: paginatedBlogs,
        pagination: {
          page,
          limit,
          total: blogs.length,
          totalPages: Math.ceil(blogs.length / limit)
        }
      }));
      
    } else if (pathname === '/.netlify/functions/blog-get') {
      // Get single blog
      const id = parsedUrl.query.id;
      const slug = parsedUrl.query.slug;
      
      let blog = null;
      if (id) {
        blog = blogs.find(b => b.id === id);
      } else if (slug) {
        blog = blogs.find(b => b.slug === slug);
      }
      
      if (blog) {
        res.statusCode = 200;
        res.end(JSON.stringify({
          success: true,
          data: blog
        }));
      } else {
        res.statusCode = 404;
        res.end(JSON.stringify({
          success: false,
          error: 'Blog not found'
        }));
      }
      
    } else if (pathname === '/.netlify/functions/blog-create' && req.method === 'POST') {
      // Create new blog
      const data = JSON.parse(body);
      const newBlog = {
        id: (blogs.length + 1).toString(),
        title: data.title,
        content: data.content,
        slug: data.slug || data.title.toLowerCase().replace(/\s+/g, '-'),
        author: data.author || 'وسيم',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        love_count: 0
      };
      
      blogs.push(newBlog);
      
      res.statusCode = 201;
      res.end(JSON.stringify({
        success: true,
        data: newBlog
      }));
      
    } else if (pathname === '/.netlify/functions/blog-love' && req.method === 'POST') {
      // Increment love count
      const data = JSON.parse(body);
      const blog = blogs.find(b => b.id === data.id);
      
      if (blog) {
        blog.love_count++;
        res.statusCode = 200;
        res.end(JSON.stringify({
          success: true,
          data: blog
        }));
      } else {
        res.statusCode = 404;
        res.end(JSON.stringify({
          success: false,
          error: 'Blog not found'
        }));
      }
      
    } else if (pathname === '/' || pathname === '/index.html') {
      // Serve index.html
      try {
        const indexPath = path.join(__dirname, 'public', 'index.html');
        const content = fs.readFileSync(indexPath, 'utf8');
        res.setHeader('Content-Type', 'text/html');
        res.statusCode = 200;
        res.end(content);
      } catch (error) {
        res.statusCode = 200;
        res.setHeader('Content-Type', 'text/html');
        res.end(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>Portfolio Blog API</title>
            <meta charset="UTF-8">
          </head>
          <body>
            <h1>🚀 Portfolio Blog API</h1>
            <p>السيرفر شغال بنجاح!</p>
            <h2>API Endpoints:</h2>
            <ul>
              <li><a href="/.netlify/functions/blogs">GET /.netlify/functions/blogs</a> - جلب جميع المقالات</li>
              <li>GET /.netlify/functions/blog-get?id=1 - جلب مقال واحد</li>
              <li>POST /.netlify/functions/blog-create - إنشاء مقال جديد</li>
              <li>POST /.netlify/functions/blog-love - زيادة عدد الإعجابات</li>
            </ul>
          </body>
          </html>
        `);
      }
      
    } else {
      // Serve static files or 404
      try {
        const filePath = path.join(__dirname, 'public', pathname);
        const content = fs.readFileSync(filePath);
        const ext = path.extname(filePath);
        
        if (ext === '.css') {
          res.setHeader('Content-Type', 'text/css');
        } else if (ext === '.js') {
          res.setHeader('Content-Type', 'application/javascript');
        } else if (ext === '.html') {
          res.setHeader('Content-Type', 'text/html');
        }
        
        res.statusCode = 200;
        res.end(content);
      } catch (error) {
        res.statusCode = 404;
        res.end(JSON.stringify({
          success: false,
          error: 'Not found'
        }));
      }
    }
    
  } catch (error) {
    console.error('Server error:', error);
    res.statusCode = 500;
    res.end(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }));
  }
});

server.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📝 API endpoints available at http://localhost:${PORT}/.netlify/functions/`);
  console.log(`🌐 Frontend available at http://localhost:${PORT}`);
  console.log(`✅ المشروع شغال بنجاح!`);
});
