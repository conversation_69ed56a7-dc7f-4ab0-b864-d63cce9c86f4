<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog API Backend</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .endpoint {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .method {
            font-weight: bold;
            color: #007bff;
        }
        .url {
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Blog Serverless Backend</h1>
        <p>Your blog API backend is running successfully!</p>
        
        <h2>Available Endpoints:</h2>
        
        <div class="endpoint">
            <div class="method">GET</div>
            <div class="url">/.netlify/functions/blogs</div>
            <div>Get all blogs with pagination</div>
        </div>
        
        <div class="endpoint">
            <div class="method">GET</div>
            <div class="url">/.netlify/functions/blog-get?id=UUID</div>
            <div>Get single blog by ID or slug</div>
        </div>
        
        <div class="endpoint">
            <div class="method">POST</div>
            <div class="url">/.netlify/functions/blog-create</div>
            <div>Create new blog post</div>
        </div>
        
        <div class="endpoint">
            <div class="method">PUT</div>
            <div class="url">/.netlify/functions/blog-update?id=UUID</div>
            <div>Update existing blog post</div>
        </div>
        
        <div class="endpoint">
            <div class="method">DELETE</div>
            <div class="url">/.netlify/functions/blog-delete?id=UUID</div>
            <div>Delete blog post</div>
        </div>
        
        <div class="endpoint">
            <div class="method">POST</div>
            <div class="url">/.netlify/functions/blog-love?id=UUID</div>
            <div>Increment blog loves counter</div>
        </div>
        
        <h2>Database Configuration Required</h2>
        <p>Make sure to set your <strong>DATABASE_URL</strong> environment variable in Netlify with your PostgreSQL connection string.</p>
        
        <h2>Ready for Deployment</h2>
        <p>This backend is ready to be deployed to Netlify. All endpoints include CORS headers for frontend integration.</p>
    </div>
</body>
</html>
