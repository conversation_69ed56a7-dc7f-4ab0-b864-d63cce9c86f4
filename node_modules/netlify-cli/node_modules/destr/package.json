{"name": "destr", "version": "2.0.2", "description": "A faster, secure and convenient alternative for JSON.parse", "repository": "unjs/destr", "license": "MIT", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./lib/index.cjs"}}, "main": "./lib/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "lib"], "scripts": {"bench:bun": "pnpm build && bun --bun ./bench.mjs", "bench:node": "pnpm build && node ./bench.mjs", "build": "unbuild", "dev": "vitest dev", "lint": "eslint --ext .ts . && prettier -c src test", "lint:fix": "eslint --ext .ts . --fix && prettier -w src test", "release": "pnpm test && pnpm build && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage"}, "devDependencies": {"@hapi/bourne": "^3.0.0", "@vitest/coverage-v8": "^0.34.6", "benchmark": "^2.1.4", "changelogen": "^0.5.5", "eslint": "^8.52.0", "eslint-config-unjs": "^0.2.1", "prettier": "^3.0.3", "secure-json-parse": "^2.7.0", "typescript": "^5.2.2", "unbuild": "^2.0.0", "vitest": "^0.34.6"}, "packageManager": "pnpm@8.9.2"}