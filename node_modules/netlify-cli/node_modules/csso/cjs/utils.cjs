'use strict';

const processSelector = require('./restructure/prepare/processSelector.cjs');
const utils$1 = require('./restructure/utils.cjs');



exports.processSelector = processSelector;
exports.addSelectors = utils$1.addSelectors;
exports.compareDeclarations = utils$1.compareDeclarations;
exports.hasSimilarSelectors = utils$1.hasSimilarSelectors;
exports.isEqualDeclarations = utils$1.isEqualDeclarations;
exports.isEqualSelectors = utils$1.isEqualSelectors;
exports.unsafeToSkipNode = utils$1.unsafeToSkipNode;
