{"name": "crossws", "version": "0.1.1", "description": "Cross-platform WebSocket Servers for Node.js, Deno, Bun and Cloudflare Workers", "repository": "unjs/crossws", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./websocket": {"types": {"import": "./dist/websocket/index.d.mts", "require": "./dist/websocket/index.d.cts"}, "browser": "./dist/websocket/index.mjs", "worker": "./dist/websocket/index.mjs", "bun": "./dist/websocket/index.mjs", "deno": "./dist/websocket/index.mjs", "edge-light": "./dist/websocket/index.mjs", "workerd": "./dist/websocket/index.mjs", "node": {"import": "./dist/websocket/node.mjs", "require": "./dist/websocket/node.cjs"}, "import": "./dist/websocket/index.mjs", "require": "./dist/websocket/index.cjs"}, "./adapters/bun": {"types": "./dist/adapters/bun.d.ts", "import": "./dist/adapters/bun.mjs", "require": "./dist/adapters/bun.cjs"}, "./adapters/deno": {"types": "./dist/adapters/deno.d.ts", "import": "./dist/adapters/deno.mjs", "require": "./dist/adapters/deno.cjs"}, "./adapters/cloudflare": {"types": "./dist/adapters/cloudflare.d.ts", "import": "./dist/adapters/cloudflare.mjs", "require": "./dist/adapters/cloudflare.cjs"}, "./adapters/node": {"types": "./dist/adapters/node.d.ts", "import": "./dist/adapters/node.mjs", "require": "./dist/adapters/node.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "adapters", "*.d.ts"], "scripts": {"build": "unbuild", "play:node": "jiti playground/node.ts", "play:bun": "bun playground/bun.ts", "play:deno": "deno run -A playground/deno.ts", "play:cf": "wrangler dev --port 3001", "lint": "eslint --cache --ext .ts,.js,.mjs,.cjs . && prettier -c src", "lint:fix": "eslint --cache --ext .ts,.js,.mjs,.cjs . --fix && prettier -c src -w", "prepack": "pnpm run build", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && pnpm test:types", "test:types": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240117.0", "@types/bun": "^1.0.4", "@types/node": "^20.11.10", "@types/web": "^0.0.135", "@types/ws": "^8.5.10", "changelogen": "^0.5.5", "consola": "^3.2.3", "eslint": "^8.56.0", "eslint-config-unjs": "^0.2.1", "jiti": "^1.21.0", "prettier": "^3.2.4", "typescript": "^5.3.3", "uWebSockets.js": "github:uNetworking/uWebSockets.js#v20.33.0", "unbuild": "^2.0.0", "wrangler": "^3.25.0", "ws": "^8.16.0"}, "packageManager": "pnpm@8.15.0"}