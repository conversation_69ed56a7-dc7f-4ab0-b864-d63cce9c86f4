declare class WebSocketError extends Error {
    constructor(...args: any[]);
}

declare class WebSocketMessage {
    readonly rawData: string | ArrayBuffer | Uint8Array;
    readonly isBinary?: boolean | undefined;
    constructor(rawData: string | ArrayBuffer | Uint8Array, isBinary?: boolean | undefined);
    text(): string;
    toString(): string;
}

type ReadyState = 0 | 1 | 2 | 3;
interface WebSocketContext {
}
declare abstract class WebSocketPeerBase<T extends WebSocketContext = WebSocketContext> {
    ctx: T;
    constructor(ctx: T);
    get id(): string | undefined;
    get readyState(): ReadyState | -1;
    abstract send(message: string | ArrayBuffer | Uint8Array, compress?: boolean): number;
    toString(): string;
}

type WSHook<ArgsT extends Array<any> = []> = (peer: WebSocketPeerBase, ...args: ArgsT) => void | Promise<void>;
declare function defineWebSocketHooks(hooks: Partial<WebSocketHooks>): Partial<WebSocketHooks>;
interface WebSocketHooks {
    /** A message is received */
    message: WSHook<[WebSocketMessage]>;
    /** A socket is opened */
    open: WSHook<[]>;
    /** A socket is closed */
    close: WSHook<[{
        code?: number;
        reason?: string;
    }]>;
    /** An error occurs */
    error: WSHook<[WebSocketError]>;
    "bun:message": WSHook<[ws: any, message: any]>;
    "bun:open": WSHook<[ws: any]>;
    "bun:close": WSHook<[ws: any]>;
    "bun:drain": WSHook<[]>;
    "bun:error": WSHook<[ws: any, error: any]>;
    "bun:ping": WSHook<[ws: any, data: any]>;
    "bun:pong": WSHook<[ws: any, data: any]>;
    "cloudflare:accept": WSHook<[]>;
    "cloudflare:message": WSHook<[event: any]>;
    "cloudflare:error": WSHook<[event: any]>;
    "cloudflare:close": WSHook<[event: any]>;
    "deno:open": WSHook<[]>;
    "deno:message": WSHook<[event: any]>;
    "deno:close": WSHook<[]>;
    "deno:error": WSHook<[error: any]>;
    "node:open": WSHook<[]>;
    "node:message": WSHook<[data: any, isBinary: boolean]>;
    "node:close": WSHook<[code: number, reason: Buffer]>;
    "node:error": WSHook<[error: any]>;
    "node:ping": WSHook<[data: Buffer]>;
    "node:pong": WSHook<[data: Buffer]>;
    "node:unexpected-response": WSHook<[req: any, res: any]>;
    "node:upgrade": WSHook<[req: any]>;
}

type WebSocketAdapter<RT = any, OT = any> = (hooks: Partial<WebSocketHooks>, opts: OT) => RT;
declare function defineWebSocketAdapter<RT, OT>(factory: WebSocketAdapter<RT, OT>): WebSocketAdapter<RT, OT>;

export { type WebSocketAdapter, type WebSocketContext, WebSocketError, type WebSocketHooks, WebSocketMessage, WebSocketPeerBase, defineWebSocketAdapter, defineWebSocketHooks };
