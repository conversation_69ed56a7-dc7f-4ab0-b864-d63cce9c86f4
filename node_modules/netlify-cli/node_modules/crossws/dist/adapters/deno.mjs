import { d as defineWebSocketAdapter, a as WebSocketMessage, W as WebSocketError, b as WebSocketPeerBase } from '../shared/crossws.9536f626.mjs';

const deno = defineWebSocketAdapter(
  (hooks, opts = {}) => {
    const handleUpgrade = (request) => {
      const upgrade = Deno.upgradeWebSocket(request);
      const peer = new DenoWebSocketPeer({
        deno: { ws: upgrade.socket, request }
      });
      upgrade.socket.addEventListener("open", () => {
        hooks["deno:open"]?.(peer);
        hooks.open?.(peer);
      });
      upgrade.socket.addEventListener("message", (event) => {
        hooks["deno:message"]?.(peer, event);
        hooks.message?.(peer, new WebSocketMessage(event.data));
      });
      upgrade.socket.addEventListener("close", () => {
        hooks["deno:close"]?.(peer);
        hooks.close?.(peer, {});
      });
      upgrade.socket.addEventListener("error", (error) => {
        hooks["deno:error"]?.(peer, error);
        hooks.error?.(peer, new WebSocketError(error));
      });
      return upgrade.response;
    };
    return {
      handleUpgrade
    };
  }
);
class DenoWebSocketPeer extends WebSocketPeerBase {
  get id() {
    return this.ctx.deno.ws.remoteAddress;
  }
  get readyState() {
    return this.ctx.deno.ws.readyState;
  }
  send(message) {
    this.ctx.deno.ws.send(message);
    return 0;
  }
}

export { deno as default };
