import { d as defineWebSocketAdapter, a as WebSocketMessage, W as WebSocketError, b as WebSocketPeerBase } from '../shared/crossws.9536f626.mjs';

const cloudflare = defineWebSocketAdapter(
  (hooks, opts = {}) => {
    const handleUpgrade = (request, env, context) => {
      const pair = new WebSocketPair();
      const client = pair[0];
      const server = pair[1];
      const peer = new CloudflareWebSocketPeer({
        cloudflare: { client, server, request, env, context }
      });
      server.accept();
      hooks["cloudflare:accept"]?.(peer);
      hooks.open?.(peer);
      server.addEventListener("message", (event) => {
        hooks["cloudflare:message"]?.(peer, event);
        hooks.message?.(peer, new WebSocketMessage(event.data));
      });
      server.addEventListener("error", (event) => {
        hooks["cloudflare:error"]?.(peer, event);
        hooks.error?.(peer, new WebSocketError(event.error));
      });
      server.addEventListener("close", (event) => {
        hooks["cloudflare:close"]?.(peer, event);
        hooks.close?.(peer, { code: event.code, reason: event.reason });
      });
      return new Response(null, {
        status: 101,
        webSocket: client
      });
    };
    return {
      handleUpgrade
    };
  }
);
class CloudflareWebSocketPeer extends WebSocketPeerBase {
  get id() {
    return void 0;
  }
  get readyState() {
    return this.ctx.cloudflare.client.readyState;
  }
  send(message) {
    this.ctx.cloudflare.server.send(message);
    return 0;
  }
}

export { cloudflare as default };
