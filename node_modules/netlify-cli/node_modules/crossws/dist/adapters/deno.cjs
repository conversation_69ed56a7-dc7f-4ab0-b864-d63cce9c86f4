'use strict';

const peer = require('../shared/crossws.21e14e0d.cjs');

const deno = peer.defineWebSocketAdapter(
  (hooks, opts = {}) => {
    const handleUpgrade = (request) => {
      const upgrade = Deno.upgradeWebSocket(request);
      const peer$1 = new DenoWebSocketPeer({
        deno: { ws: upgrade.socket, request }
      });
      upgrade.socket.addEventListener("open", () => {
        hooks["deno:open"]?.(peer$1);
        hooks.open?.(peer$1);
      });
      upgrade.socket.addEventListener("message", (event) => {
        hooks["deno:message"]?.(peer$1, event);
        hooks.message?.(peer$1, new peer.WebSocketMessage(event.data));
      });
      upgrade.socket.addEventListener("close", () => {
        hooks["deno:close"]?.(peer$1);
        hooks.close?.(peer$1, {});
      });
      upgrade.socket.addEventListener("error", (error) => {
        hooks["deno:error"]?.(peer$1, error);
        hooks.error?.(peer$1, new peer.WebSocketError(error));
      });
      return upgrade.response;
    };
    return {
      handleUpgrade
    };
  }
);
class DenoWebSocketPeer extends peer.WebSocketPeerBase {
  get id() {
    return this.ctx.deno.ws.remoteAddress;
  }
  get readyState() {
    return this.ctx.deno.ws.readyState;
  }
  send(message) {
    this.ctx.deno.ws.send(message);
    return 0;
  }
}

module.exports = deno;
