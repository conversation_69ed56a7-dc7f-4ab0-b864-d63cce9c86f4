import { WebSocketAdapter, WebSocketPeerBase } from '../index.cjs';
import { WebSocketHandler, ServerWebSocket } from 'bun';

interface AdapterOptions {
}
type ContextData = {
    _peer?: WebSocketPeer;
};
interface Adapter {
    websocket: WebSocketHandler<ContextData>;
}
declare const _default: WebSocketAdapter<Adapter, AdapterOptions>;

declare class WebSocketPeer extends WebSocketPeerBase<{
    bun: {
        ws: ServerWebSocket<ContextData>;
    };
}> {
    get id(): string;
    get readyState(): any;
    send(message: string | ArrayBuffer): number;
}

export { type Adapter, type AdapterOptions, _default as default };
