'use strict';

const peer = require('../shared/crossws.21e14e0d.cjs');

const bun = peer.defineWebSocketAdapter(
  (hooks, opts = {}) => {
    const getPeer = (ws) => {
      if (ws.data?._peer) {
        return ws.data._peer;
      }
      const peer = new WebSocketPeer({ bun: { ws } });
      ws.data = ws.data || {};
      ws.data._peer = peer;
      return peer;
    };
    return {
      websocket: {
        message: (ws, message) => {
          const peer$1 = getPeer(ws);
          hooks["bun:message"]?.(peer$1, ws, message);
          hooks.message?.(peer$1, new peer.WebSocketMessage(message));
        },
        open: (ws) => {
          const peer = getPeer(ws);
          hooks["bun:open"]?.(peer, ws);
          hooks.open?.(peer);
        },
        close: (ws) => {
          const peer = getPeer(ws);
          hooks["bun:close"]?.(peer, ws);
          hooks.close?.(peer, {});
        },
        drain: (ws) => {
          const peer = getPeer(ws);
          hooks["bun:drain"]?.(peer);
        },
        // @ts-expect-error types unavailable but mentioned in docs
        error: (ws, error) => {
          const peer$1 = getPeer(ws);
          hooks["bun:error"]?.(peer$1, ws, error);
          hooks.error?.(peer$1, new peer.WebSocketError(error));
        },
        ping(ws, data) {
          const peer = getPeer(ws);
          hooks["bun:ping"]?.(peer, ws, data);
        },
        pong(ws, data) {
          const peer = getPeer(ws);
          hooks["bun:pong"]?.(peer, ws, data);
        }
      }
    };
  }
);
class WebSocketPeer extends peer.WebSocketPeerBase {
  get id() {
    let addr = this.ctx.bun.ws.remoteAddress;
    if (addr.includes(":")) {
      addr = `[${addr}]`;
    }
    return addr;
  }
  get readyState() {
    return this.ctx.bun.ws.readyState;
  }
  send(message) {
    this.ctx.bun.ws.send(message);
    return 0;
  }
}

module.exports = bun;
