import { WebSocketAdapter } from '../index.cjs';
import * as _cf from '@cloudflare/workers-types';

type Env = Record<string, any>;
interface AdapterOptions {
}
interface Adapter {
    handleUpgrade(req: _cf.Request, env: Env, context: _cf.ExecutionContext): _cf.Response;
}
declare const _default: WebSocketAdapter<Adapter, AdapterOptions>;

export { type Adapter, type AdapterOptions, _default as default };
