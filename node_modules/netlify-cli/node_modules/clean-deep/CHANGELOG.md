# Changelog

## [v3.4.0](https://github.com/nunofgs/clean-deep/releases/tag/v3.4.0) (2020-08-19)
- Bump acorn from 5.7.3 to 5.7.4 [\#39](https://github.com/nunofgs/clean-deep/pull/39) ([dependabot[bot]](https://github.com/apps/dependabot))
- Bump lodash from 4.17.15 to 4.17.19 [\#44](https://github.com/nunofgs/clean-deep/pull/44) ([dependabot[bot]](https://github.com/apps/dependabot))

## [v3.3.0](https://github.com/nunofgs/clean-deep/releases/tag/v3.3.0) (2020-02-11)
- Update https-proxy-agent@2.2.4 [\#38](https://github.com/nunofgs/clean-deep/pull/38) ([nunofgs](https://github.com/nunofgs))
- Bump handlebars from 4.1.2 to 4.5.3 [\#36](https://github.com/nunofgs/clean-deep/pull/36) ([dependabot[bot]](https://github.com/apps/dependabot))
- TypeScript declaration file [\#37](https://github.com/nunofgs/clean-deep/pull/37) ([FFKL](https://github.com/FFKL))

## [v3.2.0](https://github.com/nunofgs/clean-deep/releases/tag/v3.2.0) (2019-12-04)
- add cleanKeys option [\#35](https://github.com/nunofgs/clean-deep/pull/35) ([kalimantos](https://github.com/kalimantos))
- Fixed wrong wording [\#32](https://github.com/nunofgs/clean-deep/pull/32) ([lindekaer](https://github.com/lindekaer))

## [v3.1.0](https://github.com/nunofgs/clean-deep/releases/tag/v3.1.0) (2019-10-15)
- Add custom values support [\#31](https://github.com/nunofgs/clean-deep/pull/31) ([nunofgs](https://github.com/nunofgs))

## [v3.0.5](https://github.com/nunofgs/clean-deep/releases/tag/v3.0.5) (2019-09-10)
- Move jest to dev dependencies [\#30](https://github.com/nunofgs/clean-deep/pull/30) ([nunofgs](https://github.com/nunofgs))

## [v3.0.4](https://github.com/nunofgs/clean-deep/releases/tag/v3.0.4) (2019-08-27)

## [v3.0.3](https://github.com/nunofgs/clean-deep/releases/tag/v3.0.3) (2019-08-27)

## [v3.0.2](https://github.com/nunofgs/clean-deep/releases/tag/v3.0.2) (2019-08-27)
- Update to modern standards [\#27](https://github.com/nunofgs/clean-deep/pull/27) ([nunofgs](https://github.com/nunofgs))
- Removed test for not implemented functionality [\#26](https://github.com/nunofgs/clean-deep/pull/26) ([mikaello](https://github.com/mikaello))
- Replace lodash.isarray with Array.isArray() [\#25](https://github.com/nunofgs/clean-deep/pull/25) ([elrumordelaluz](https://github.com/elrumordelaluz))

## [v3.0.1](https://github.com/nunofgs/clean-deep/releases/tag/v3.0.1) (2017-07-18)
- Update ownership [\#23](https://github.com/nunofgs/clean-deep/pull/23) ([nunofgs](https://github.com/nunofgs))

## [v3.0.0](https://github.com/nunofgs/clean-deep/releases/tag/v3.0.0) (2017-04-26)
- Clean and traverse arrays [\#22](https://github.com/nunofgs/clean-deep/pull/22) ([nunofgs](https://github.com/nunofgs))

## [v2.0.2](https://github.com/nunofgs/clean-deep/releases/tag/v2.0.2) (2017-03-09)
- Update dist to es5 to keep uglifyjs compatibility [\#20](https://github.com/nunofgs/clean-deep/pull/20) ([akofman](https://github.com/akofman))

## [v2.0.1](https://github.com/nunofgs/clean-deep/releases/tag/v2.0.1) (2016-11-02)
- Fix recursive array cleaning [\#17](https://github.com/nunofgs/clean-deep/pull/17) ([zeke](https://github.com/zeke))
- Upgrade babel-eslint to avoid 'estraverse-fb' error [\#18](https://github.com/nunofgs/clean-deep/pull/18) ([callmevlad](https://github.com/callmevlad))

## [v2.0.0](https://github.com/nunofgs/clean-deep/releases/tag/v2.0.0) (2016-10-02)
- Add .npmignore [\#14](https://github.com/nunofgs/clean-deep/pull/14) ([ruimarinho](https://github.com/ruimarinho))
- Add version script [\#12](https://github.com/nunofgs/clean-deep/pull/12) ([ruimarinho](https://github.com/ruimarinho))
- Add coverage report [\#11](https://github.com/nunofgs/clean-deep/pull/11) ([ruimarinho](https://github.com/ruimarinho))
- Update test dependencies [\#10](https://github.com/nunofgs/clean-deep/pull/10) ([ruimarinho](https://github.com/ruimarinho))
- Update lowest node supported version to 4+ [\#9](https://github.com/nunofgs/clean-deep/pull/9) ([ruimarinho](https://github.com/ruimarinho))
- Sort package.json [\#8](https://github.com/nunofgs/clean-deep/pull/8) ([ruimarinho](https://github.com/ruimarinho))
- Update README with missing options [\#13](https://github.com/nunofgs/clean-deep/pull/13) ([ruimarinho](https://github.com/ruimarinho))
- Update package.json to reflect MIT license [\#7](https://github.com/nunofgs/clean-deep/pull/7) ([ruimarinho](https://github.com/ruimarinho))
- Deprecate jscs in favour of eslint [\#6](https://github.com/nunofgs/clean-deep/pull/6) ([ruimarinho](https://github.com/ruimarinho))
- Add option to exclude empty arrays [\#5](https://github.com/nunofgs/clean-deep/pull/5) ([zeke](https://github.com/zeke))

## [v1.0.0](https://github.com/nunofgs/clean-deep/releases/tag/1.0.0) (2016-09-09)
- Add `transform-es2015-arrow-functions` babel plugin [\#2](https://github.com/nunofgs/clean-deep/pull/2) ([ruipenso](https://github.com/ruipenso))
