{"name": "clean-deep", "version": "3.4.0", "description": "Remove falsy, empty or nullable values from objects", "keywords": ["clean", "clean-deep", "deep"], "homepage": "https://github.com/nunofgs/clean-deep", "bugs": {"url": "https://github.com/nunofgs/clean-deep/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "./src/index.js", "repository": {"type": "git", "url": "https://github.com/nunofgs/clean-deep.git"}, "scripts": {"changelog": "github-changelog-generator --owner=nunofgs --repo=clean-deep --future-release=v$npm_package_version > CHANGELOG.md", "lint": "eslint .", "test": "jest", "version": "yarn changelog && git add -A CHANGELOG.md"}, "dependencies": {"lodash.isempty": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.transform": "^4.6.0"}, "devDependencies": {"@uphold/github-changelog-generator": "^0.8.1", "eslint-config-uphold": "^1.0.0", "eslint": "^6.2.2", "husky": "^3.0.4", "jest": "^24.9.0", "lint-staged": "^9.2.5"}, "engines": {"node": ">=4"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["yarn lint --fix", "git add"]}, "jest": {"collectCoverage": true, "modulePaths": ["<rootDir>"], "testEnvironment": "node", "testRegex": "test/.*\\.test.js$"}}