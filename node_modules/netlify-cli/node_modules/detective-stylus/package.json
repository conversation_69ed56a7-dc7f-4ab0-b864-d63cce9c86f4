{"name": "detective-stylus", "version": "4.0.0", "description": "Get the dependencies of a Stylus file", "main": "index.js", "files": ["index.js"], "scripts": {"lint": "xo", "fix": "xo --fix", "mocha": "mocha", "test": "npm run lint && npm run mocha", "test:ci": "c8 npm run mocha"}, "repository": {"type": "git", "url": "git+https://github.com/dependents/node-detective-stylus.git"}, "keywords": ["stylus", "ast", "static", "analysis", "dependencies"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dependents/node-detective-stylus/issues"}, "homepage": "https://github.com/dependents/node-detective-stylus", "engines": {"node": ">=14"}, "devDependencies": {"c8": "^7.13.0", "mocha": "^10.2.0", "xo": "^0.54.2"}, "xo": {"space": true, "ignores": ["test/fixtures/*"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "curly": ["error", "multi-line"], "operator-linebreak": ["error", "after"], "object-curly-spacing": ["error", "always"], "space-before-function-paren": ["error", "never"], "unicorn/prefer-module": "off", "unicorn/prefer-node-protocol": "off", "unicorn/prefer-top-level-await": "off", "unicorn/prevent-abbreviations": "off"}}}