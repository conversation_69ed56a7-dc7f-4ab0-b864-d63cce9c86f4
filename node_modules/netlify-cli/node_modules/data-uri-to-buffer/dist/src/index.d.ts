/// <reference types="node" />
export interface MimeBuffer extends Buffer {
    type: string;
    typeFull: string;
    charset: string;
}
/**
 * Returns a `Buffer` instance from the given data URI `uri`.
 *
 * @param {String} uri Data URI to turn into a Buffer instance
 * @returns {Buffer} Buffer instance from Data URI
 * @api public
 */
export declare function dataUriToBuffer(uri: string): MimeBuffer;
export default dataUriToBuffer;
