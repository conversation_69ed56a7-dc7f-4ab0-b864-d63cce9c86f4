{"name": "@sindresorhus/slugify", "version": "2.2.1", "description": "Slugify a string", "license": "MIT", "repository": "sindresorhus/slugify", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "overridable-replacements.js"], "keywords": ["string", "slugify", "slug", "url", "url-safe", "urlify", "transliterate", "transliteration", "deburr", "unicode", "ascii", "text", "decamelize", "pretty", "clean", "filename", "id"], "dependencies": {"@sindresorhus/transliterate": "^1.0.0", "escape-string-regexp": "^5.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "xo": {"rules": {"@typescript-eslint/member-ordering": "off"}}}