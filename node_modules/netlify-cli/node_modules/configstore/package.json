{"name": "configstore", "version": "6.0.0", "description": "Easily load and save config without having to think about where and how", "license": "BSD-2-<PERSON><PERSON>", "repository": "yeoman/configstore", "funding": "https://github.com/yeoman/configstore?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["config", "store", "storage", "configuration", "settings", "preferences", "json", "data", "persist", "persistent", "save"], "dependencies": {"dot-prop": "^6.0.1", "graceful-fs": "^4.2.6", "unique-string": "^3.0.0", "write-file-atomic": "^3.0.3", "xdg-basedir": "^5.0.1"}, "devDependencies": {"ava": "^3.15.0", "xo": "^0.38.2"}, "ava": {"serial": true}}