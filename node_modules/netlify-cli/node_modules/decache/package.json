{"name": "decache", "version": "4.6.2", "description": "decache (Delete Cache) lets you delete modules from node.js require() cache; useful when testing your modules/projects.", "main": "decache.js", "scripts": {"coverage": "nyc tape ./test/test.js && nyc check-coverage", "nocov": "tape ./test/test.js", "test": "nyc tape ./test/test.js | tap-nyc"}, "repository": {"type": "git", "url": "git+https://github.com/dwyl/decache.git"}, "keywords": ["delete", "node.js", "require", "cache", "uncache", "un-require"], "author": "https://github.com/dwyl/decache/graphs/contributors", "license": "MIT", "bugs": {"url": "https://github.com/dwyl/decache/issues"}, "homepage": "https://github.com/dwyl/decache#readme", "directories": {"test": "test"}, "devDependencies": {"modern-syslog": "^1.2.0", "nyc": "^15.1.0", "pre-commit": "^1.2.2", "tap-nyc": "^1.0.3", "tap-spec": "^5.0.0", "tape": "^5.3.2"}, "pre-commit": ["coverage"], "dependencies": {"callsite": "^1.0.0"}, "nyc": {"check-coverage": true, "lines": 100, "statements": 100, "functions": 100, "branches": 100, "include": ["decache.js", "setup.js", "lib/mymodule/index.js", "lib/othermodule.js"], "exclude": ["test/test.js"], "reporter": ["lcov", "text-summary"], "cache": false, "all": true, "report-dir": "./coverage"}}