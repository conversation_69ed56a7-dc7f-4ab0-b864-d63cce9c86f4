{"name": "@types/express-serve-static-core", "version": "4.17.28", "description": "TypeScript definitions for Express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/micksatana", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/samijaber", "githubUsername": "sami<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JoseLion", "githubUsername": "JoseLion"}, {"name": "<PERSON>", "url": "https://github.com/dwrss", "githubUsername": "dwrss"}, {"name": "<PERSON>", "url": "https://github.com/andoshin11", "githubUsername": "andoshin11"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-serve-static-core"}, "scripts": {}, "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*"}, "typesPublisherContentHash": "e8b75bce16c2593d8d5f9521ddcf5db42875f8406701ed9eca5178f1b74e91b9", "typeScriptVersion": "3.8"}