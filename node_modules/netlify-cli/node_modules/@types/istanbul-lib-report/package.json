{"name": "@types/istanbul-lib-report", "version": "3.0.0", "description": "TypeScript definitions for istanbul-lib-report", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jason0x43", "githubUsername": "jason0x43"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/zache", "githubUsername": "zache"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/istanbul-lib-report"}, "scripts": {}, "dependencies": {"@types/istanbul-lib-coverage": "*"}, "typesPublisherContentHash": "f8b2f5e15a24d9f52a96c5cfadb0f582bf6200ce8643e15422c3c8f1a2bb1c63", "typeScriptVersion": "2.8"}