# Installation
> `npm install --save @types/http-proxy`

# Summary
This package contains type definitions for node-http-proxy (https://github.com/nodejitsu/node-http-proxy).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-proxy.

### Additional Details
 * Last updated: Tu<PERSON>, 14 Dec 2021 22:01:06 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node)
 * Global values: none

# Credits
These definitions were written by [<PERSON><PERSON> LUCE](https://github.com/SomaticIT), [<PERSON><PERSON><PERSON>](https://github.com/Raigen), [<PERSON>](https://github.com/<PERSON>), [<PERSON>](https://github.com/jabreu610), and [<PERSON>](https://github.com/bodinsamuel).
