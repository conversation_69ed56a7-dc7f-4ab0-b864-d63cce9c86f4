{"name": "@types/retry", "version": "0.12.1", "description": "TypeScript definitions for retry", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/retry", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/krenor", "githubUsername": "krenor"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/retry"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "1f47bb1a205340c2ce3816cbd526a396c6abccdd0aa138a440aa244f7da840e1", "typeScriptVersion": "3.6"}