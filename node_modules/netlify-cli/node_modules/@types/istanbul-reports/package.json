{"name": "@types/istanbul-reports", "version": "3.0.1", "description": "TypeScript definitions for istanbul-reports", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-reports", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jason0x43", "githubUsername": "jason0x43"}, {"name": "<PERSON>", "url": "https://github.com/not-a-doctor", "githubUsername": "not-a-doctor"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/istanbul-reports"}, "scripts": {}, "dependencies": {"@types/istanbul-lib-report": "*"}, "typesPublisherContentHash": "b331eb26db90bca3bd6f1e18a10a4f37631f149624847439756763800996e143", "typeScriptVersion": "3.6"}