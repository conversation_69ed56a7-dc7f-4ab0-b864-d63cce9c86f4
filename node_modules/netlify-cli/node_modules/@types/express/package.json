{"name": "@types/express", "version": "4.17.13", "description": "TypeScript definitions for Express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "df0c9de39b435f4152916282f0ae9e98f0548d6b50f6bb6aedddc52e4e3f25a7", "typeScriptVersion": "3.6"}