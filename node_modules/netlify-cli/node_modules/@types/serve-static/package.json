{"name": "@types/serve-static", "version": "1.13.10", "description": "TypeScript definitions for serve-static", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/urossmolnik", "githubUsername": "urossmolnik"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LinusU", "githubUsername": "LinusU"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/devanshj", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-static"}, "scripts": {}, "dependencies": {"@types/mime": "^1", "@types/node": "*"}, "typesPublisherContentHash": "d7ba4df44ab0b22c50e477e33159f25cc838b045f2d25519651a7d6005a9faed", "typeScriptVersion": "3.6"}