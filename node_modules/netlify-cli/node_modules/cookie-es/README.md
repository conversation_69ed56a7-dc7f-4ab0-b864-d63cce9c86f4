
# cookie-es

[![bundle size](https://flat.badgen.net/bundlephobia/minzip/cookie-es)](https://bundlephobia.com/package/cookie-es)

ESM build of [cookie](https://www.npmjs.com/package/cookie) with bundled types.

## Usage

Install:

```sh
# npm
npm i cookie-es

# yarn
yarn add cookie-es
```

Import:

```js
// ESM
import { parse, serialize } from 'cookie-es'

// CommonJS
const { parse, serialize } = require('cookie-es')
```

## License

[MIT](./LICENSE)
