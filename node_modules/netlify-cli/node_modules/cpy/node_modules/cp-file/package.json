{"name": "cp-file", "version": "9.1.0", "description": "Copy a file", "license": "MIT", "repository": "sindresorhus/cp-file", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava && tsd"}, "files": ["cp-file-error.js", "fs.js", "index.js", "index.d.ts", "progress-emitter.js"], "keywords": ["copy", "cp", "file", "clone", "fs", "stream", "file-system", "ncp", "fast", "quick", "data", "content", "contents"], "dependencies": {"graceful-fs": "^4.1.2", "make-dir": "^3.0.0", "nested-error-stacks": "^2.0.0", "p-event": "^4.1.0"}, "devDependencies": {"ava": "^2.1.0", "clear-module": "^3.1.0", "coveralls": "^3.0.4", "del": "^5.1.0", "import-fresh": "^3.0.0", "nyc": "^15.0.0", "sinon": "^9.0.0", "tsd": "^0.11.0", "uuid": "^7.0.2", "xo": "^0.28.2"}, "xo": {"rules": {"unicorn/string-content": "off"}}}