{"name": "cpy", "version": "9.0.1", "description": "Copy files", "license": "MIT", "repository": "sindresorhus/cpy", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.17.0 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["cpy-error.js", "glob-pattern.js", "index.js", "index.d.ts"], "keywords": ["copy", "cp", "cpy", "file", "files", "clone", "fs", "stream", "glob", "file-system", "ncp", "fast", "quick", "data", "content", "contents", "cpx", "directory", "directories"], "dependencies": {"arrify": "^3.0.0", "cp-file": "^9.1.0", "globby": "^13.1.1", "junk": "^4.0.0", "micromatch": "^4.0.4", "nested-error-stacks": "^2.1.0", "p-filter": "^3.0.0", "p-map": "^5.3.0"}, "devDependencies": {"ava": "^4.0.1", "proxyquire": "^2.1.3", "rimraf": "^3.0.2", "tempy": "^2.0.0", "tsd": "^0.19.1", "typescript": "^4.5.5", "xo": "^0.48.0"}}