{"name": "detective-sass", "version": "5.0.3", "description": "Find the dependencies of a sass file", "main": "index.js", "files": ["index.js"], "scripts": {"lint": "xo", "fix": "xo --fix", "uvu": "uvu test -i fixtures", "test": "npm run lint && npm run uvu", "test:ci": "c8 npm run uvu"}, "repository": {"type": "git", "url": "git+https://github.com/dependents/node-detective-sass.git"}, "keywords": ["detective", "sass", "ast", "dependencies"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dependents/node-detective-sass/issues"}, "homepage": "https://github.com/dependents/node-detective-sass", "engines": {"node": ">=14"}, "dependencies": {"gonzales-pe": "^4.3.0", "node-source-walk": "^6.0.1"}, "devDependencies": {"c8": "^7.13.0", "uvu": "^0.5.6", "xo": "^0.54.2"}, "xo": {"space": true, "ignores": ["test/fixtures/*"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "curly": ["error", "multi-line"], "operator-linebreak": ["error", "after"], "object-curly-spacing": ["error", "always"], "space-before-function-paren": ["error", "never"], "unicorn/prefer-module": "off", "unicorn/prefer-node-protocol": "off", "unicorn/prefer-top-level-await": "off", "unicorn/prevent-abbreviations": "off"}}}