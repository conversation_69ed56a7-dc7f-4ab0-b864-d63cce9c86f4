{"name": "clean-stack", "version": "4.2.0", "description": "Clean up error stack traces", "license": "MIT", "repository": "sindresorhus/clean-stack", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["clean", "stack", "trace", "traces", "error", "electron"], "dependencies": {"escape-string-regexp": "5.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "browser": {"os": false}}