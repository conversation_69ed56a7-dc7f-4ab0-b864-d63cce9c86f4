{"name": "detective-postcss", "version": "6.1.3", "description": "Detective to find dependents of CSS (PostCSS dialect)", "main": "dist/index.js", "typings": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/dependents/node-detective-postcss.git"}, "bugs": {"url": "https://github.com/dependents/node-detective-postcss/issues"}, "homepage": "https://github.com/dependents/node-detective-postcss#readme", "keywords": [], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "files": ["dist", "runkitExample.js"], "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "dependencies": {"is-url": "^1.2.4", "postcss": "^8.4.23", "postcss-values-parser": "^6.0.2"}, "devDependencies": {"@types/is-url": "^1.2.30", "@types/jest": "^28.1.8", "jest": "^28.1.3", "lint-staged": "^13.2.1", "prettier": "^2.8.8", "rimraf": "^3.0.2", "ts-jest": "^28.0.8", "typescript": "^5.0.4"}, "scripts": {"prepack": "rimraf ./dist && tsc --outDir dist --declaration --declarationDir dist", "pretest": "prettier --check .", "test": "jest --ci", "test:watch": "jest --watch --notify", "precommit": "lint-staged", "prettier": "prettier --write ."}, "runkitExampleFilename": "runkitExample.js"}