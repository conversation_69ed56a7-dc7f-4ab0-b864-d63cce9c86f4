// default time format

// format a number of seconds into hours and minutes as appropriate
module.exports = function formatTime(t, options, roundToMultipleOf){
    function round(input) {
        if (roundToMultipleOf) {
            return roundToMultipleOf * Math.round(input / roundToMultipleOf);
        } else {
            return input
        }
    }

    // leading zero padding
    function autopadding(v){
        return (options.autopaddingChar + v).slice(-2);
    }

    // > 1h ?
    if (t > 3600) {
        return autopadding(Math.floor(t / 3600)) + 'h' + autopadding(round((t % 3600) / 60)) + 'm';

    // > 60s ?
    } else if (t > 60) {
        return autopadding(Math.floor(t / 60)) + 'm' + autopadding(round((t % 60))) + 's';

    // > 10s ?
    } else if (t > 10) {
        return autopadding(round(t)) + 's';

    // default: don't apply round to multiple
    }else{
        return autopadding(t) + 's';
    }
}