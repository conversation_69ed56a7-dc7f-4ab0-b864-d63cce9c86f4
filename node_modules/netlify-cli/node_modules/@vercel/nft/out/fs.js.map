{"version": 3, "file": "fs.js", "sourceRoot": "", "sources": ["../src/fs.ts"], "names": [], "mappings": ";;;;;;AACA,+BAA+B;AAC/B,8DAA6B;AAC7B,2CAAkC;AAElC,MAAM,UAAU,GAAG,qBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACxC,MAAM,UAAU,GAAG,qBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACxC,MAAM,MAAM,GAAG,qBAAE,CAAC,QAAQ,CAAC,IAAI,CAAC;AAEhC,MAAa,gBAAgB;IAM3B,YAAY,EACV,KAAK,EACL,iBAAiB,GAQlB;QACC,IAAI,CAAC,WAAW,GAAG,IAAI,iBAAI,CAAC,iBAAiB,CAAC,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE,SAAS,IAAI,IAAI,GAAG,EAAE,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE,SAAS,IAAI,IAAI,GAAG,EAAE,CAAC;QAC/C,IAAI,CAAC,YAAY,GAAG,KAAK,EAAE,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC;QAErD,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACjC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACjC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;SACxC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,IAAY;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,MAAM,KAAK,SAAS;YAAE,OAAO,MAAM,CAAC;QACxC,kFAAkF;QAClF,gDAAgD;QAChD,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACzE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QAE7C,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,IAAY;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,MAAM,KAAK,SAAS;YAAE,OAAO,MAAM,CAAC;QACxC,kFAAkF;QAClF,gDAAgD;QAChD,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACzE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QAE1C,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,IAAY;QACrB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,MAAM,KAAK,SAAS;YAAE,OAAO,MAAM,CAAC;QACxC,kFAAkF;QAClF,gDAAgD;QAChD,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACjE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAEtC,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,IAAY;QAC1C,IAAI;YACF,MAAM,IAAI,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,CAAC;YACpC,kCAAkC;YAClC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,KAAK;gBAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAA,cAAO,EAAC,IAAI,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,CAAM,EAAE;YACf,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS;gBACpE,MAAM,CAAC,CAAC;YACV,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,IAAY;QAC1C,IAAI;YACF,OAAO,CAAC,MAAM,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;SAC5C;QAAC,OAAO,CAAM,EAAE;YACf,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAC9C,OAAO,IAAI,CAAC;aACb;YACD,MAAM,CAAC,CAAC;SACT;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,IAAY;QACtC,IAAI;YACF,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC;SAC3B;QAAC,OAAO,CAAM,EAAE;YACf,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC;aACb;YACD,MAAM,CAAC,CAAC;SACT;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CACzB,IAAY,EACZ,MAAyC;QAEzC,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAEjC,IAAI;YACF,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAChC;gBAAS;YACR,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;SAC5B;IACH,CAAC;CACF;AA9GD,4CA8GC"}