{"name": "colors-option", "version": "3.0.0", "type": "module", "exports": "./build/src/main.js", "main": "./build/src/main.js", "files": ["build/src/**/*.{js,ts,map,json,sh,md}", "examples/**/*.{js,ts,map,json,sh,md}"], "scripts": {"test": "gulp test"}, "description": "Let users toggle colors", "keywords": ["color", "colors", "option", "config", "configuration", "disable", "chalk", "kleur", "ansi", "rgb", "terminal", "tty", "shell", "bash", "cli", "es6", "javascript", "nodejs", "library", "windows"], "license": "Apache-2.0", "homepage": "https://git.io/JthtP", "repository": "ehmicky/colors-option", "bugs": {"url": "https://github.com/ehmicky/colors-option/issues"}, "author": "ehmicky <<EMAIL>> (https://github.com/ehmicky)", "directories": {"doc": "docs", "lib": "src", "test": "test"}, "devDependencies": {"@ehmicky/dev-tasks": "^1.0.57", "test-each": "^3.0.1"}, "engines": {"node": ">=12.20.0"}, "dependencies": {"chalk": "^5.0.0", "filter-obj": "^3.0.0", "is-plain-obj": "^4.0.0", "jest-validate": "^27.3.1"}}