{"version": 3, "sources": ["../../src/main.js"], "names": ["Chalk", "getOpts", "colorsOption", "opts", "colors", "stream", "chalkOpts", "level", "getLevel", "chalk", "terminalLevel", "getTerminalLevel", "undefined", "Math", "max", "isTTY", "DEPTH_TO_LEVEL", "getColorDepth"], "mappings": ";AACA,OAASA,KAAT,KAAsB,OAAtB;;AAEA,OAASC,OAAT,KAAwB,cAAxB;;;;;;;;;;;AAWA,cAAe,SAASC,CAAAA,YAAT,CAAsBC,IAAtB,CAA4B;AACzC,KAAM,CAAEC,MAAF,CAAUC,MAAV,CAAkBC,SAAlB,EAAgCL,OAAO,CAACE,IAAD,CAA7C;;AAEA,KAAMI,CAAAA,KAAK,CAAGC,QAAQ,CAACJ,MAAD,CAASC,MAAT,CAAtB;AACA,KAAMI,CAAAA,KAAK,CAAG,GAAIT,CAAAA,KAAJ,CAAU,CAAE,GAAGM,SAAL,CAAgBC,KAAhB,CAAV,CAAd;AACA,MAAOE,CAAAA,KAAP;AACD;;AAED,KAAMD,CAAAA,QAAQ,CAAG,SAAUJ,MAAV,CAAkBC,MAAlB,CAA0B;AACzC,GAAID,MAAM,GAAK,KAAf,CAAsB;AACpB,MAAO,EAAP;AACD;;AAED,KAAMM,CAAAA,aAAa,CAAGC,gBAAgB,CAACN,MAAD,CAAtC;;AAEA,GAAID,MAAM,GAAKQ,SAAf,CAA0B;AACxB,MAAOF,CAAAA,aAAP;AACD;;AAED,MAAOG,CAAAA,IAAI,CAACC,GAAL,CAASJ,aAAT,CAAwB,CAAxB,CAAP;AACD,CAZD;;AAcA,KAAMC,CAAAA,gBAAgB,CAAG,SAAUN,MAAV,CAAkB;AACzC,GAAI,CAACA,MAAM,CAACU,KAAZ,CAAmB;AACjB,MAAO,EAAP;AACD;;AAED,MAAOC,CAAAA,cAAc,CAACX,MAAM,CAACY,aAAP,EAAD,CAArB;AACD,CAND;;;AASA,KAAMD,CAAAA,cAAc,CAAG,CAAE,EAAG,CAAL,CAAQ,EAAG,CAAX,CAAc,EAAG,CAAjB,CAAoB,GAAI,CAAxB,CAAvB", "sourcesContent": ["// eslint-disable-next-line import/no-unresolved, node/no-missing-import\nimport { Chalk } from 'chalk'\n\nimport { getOpts } from './options.js'\n\n// Retrieve `chalk` instance.\n// Allows forcing `opts.colors` with `true` or `false` (default: guessed).\n// Use `stdout.getColorDepth()` instead of chalk's default behavior (relying\n// on `supports-color`) because it:\n//  - Handles the `NO_COLOR` and `NODE_DISABLE_COLORS` environment variables\n//  - Does not try to guess the `level` from CLI flags\n//  - Has a simpler priority order between CLI flags, options and environment\n//    variables\n//  - Is built-in Node.js behavior\nexport default function colorsOption(opts) {\n  const { colors, stream, chalkOpts } = getOpts(opts)\n\n  const level = getLevel(colors, stream)\n  const chalk = new Chalk({ ...chalkOpts, level })\n  return chalk\n}\n\nconst getLevel = function (colors, stream) {\n  if (colors === false) {\n    return 0\n  }\n\n  const terminalLevel = getTerminalLevel(stream)\n\n  if (colors === undefined) {\n    return terminalLevel\n  }\n\n  return Math.max(terminalLevel, 1)\n}\n\nconst getTerminalLevel = function (stream) {\n  if (!stream.isTTY) {\n    return 0\n  }\n\n  return DEPTH_TO_LEVEL[stream.getColorDepth()]\n}\n\n// Maps chalk levels to color depth\nconst DEPTH_TO_LEVEL = { 1: 0, 4: 1, 8: 2, 24: 3 }\n"], "file": "src/main.js"}