{"version": 3, "sources": ["../../src/options.js"], "names": ["stdout", "Stream", "filterObj", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validate", "getOpts", "opts", "validateOpts", "optsA", "isDefined", "colors", "colorsA", "stream", "chalkOpts", "DEFAULT_OPTS", "validateBasicOpts", "exampleConfig", "EXAMPLE_OPTS", "recursiveDenylist", "validateStream", "TypeError", "undefined", "key", "value"], "mappings": "AAAA,OAASA,MAAT,KAAuB,SAAvB;AACA,OAASC,MAAT,KAAuB,QAAvB;;AAEA,MAAOC,CAAAA,SAAP,KAAsB,YAAtB;AACA,MAAOC,CAAAA,UAAP,KAAuB,cAAvB;AACA,OAASC,QAAT,KAAyB,eAAzB;;;AAGA,MAAO,MAAMC,CAAAA,OAAO,CAAG,SAAUC,IAAI,CAAG,EAAjB,CAAqB;AAC1CC,YAAY,CAACD,IAAD,CAAZ;AACA,KAAME,CAAAA,KAAK,CAAGN,SAAS,CAACI,IAAD,CAAOG,SAAP,CAAvB;AACA,KAAM;AACJC,MAAM,CAAEC,OADJ;AAEJC,MAFI;AAGJ,GAAGC,SAHC;AAIF;AACF,GAAGC,YADD;AAEF,GAAGN,KAFD,CAJJ;;AAQA,MAAO,CAAEE,MAAM,CAAEC,OAAV,CAAmBC,MAAnB,CAA2BC,SAA3B,CAAP;AACD,CAZM;;AAcP,KAAMN,CAAAA,YAAY,CAAG,SAAUD,IAAV,CAAgB;AACnCS,iBAAiB,CAACT,IAAD,CAAjB;AACAF,QAAQ,CAACE,IAAD,CAAO,CAAEU,aAAa,CAAEC,YAAjB,CAA+BC,iBAAiB,CAAE,CAAC,QAAD,CAAlD,CAAP,CAAR;AACAC,cAAc,CAACb,IAAD,CAAd;AACD,CAJD;;AAMA,KAAMS,CAAAA,iBAAiB,CAAG,SAAUT,IAAV,CAAgB;AACxC,GAAI,CAACH,UAAU,CAACG,IAAD,CAAf,CAAuB;AACrB,KAAM,IAAIc,CAAAA,SAAJ,CAAe,mCAAkCd,IAAK,EAAtD,CAAN;AACD;AACF,CAJD;;AAMA,KAAMa,CAAAA,cAAc,CAAG,SAAU,CAAEP,MAAF,CAAV,CAAsB;AAC3C,GAAIA,MAAM,GAAKS,SAAX,EAAwB,EAAET,MAAM,WAAYX,CAAAA,MAApB,CAA5B,CAAyD;AACvD,KAAM,IAAImB,CAAAA,SAAJ,CAAe,qCAAoCR,MAAO,EAA1D,CAAN;AACD;AACF,CAJD;;AAMA,KAAMH,CAAAA,SAAS,CAAG,SAAUa,GAAV,CAAeC,KAAf,CAAsB;AACtC,MAAOA,CAAAA,KAAK,GAAKF,SAAjB;AACD,CAFD;;AAIA,KAAMP,CAAAA,YAAY,CAAG;AACnBF,MAAM,CAAEZ,MADW,CAArB;;;AAIA,KAAMiB,CAAAA,YAAY,CAAG;AACnB,GAAGH,YADgB;AAEnBJ,MAAM,CAAE,IAFW,CAArB", "sourcesContent": ["import { stdout } from 'process'\nimport { Stream } from 'stream'\n\nimport filterObj from 'filter-obj'\nimport isPlainObj from 'is-plain-obj'\nimport { validate } from 'jest-validate'\n\n// Normalize options and assign default values\nexport const getOpts = function (opts = {}) {\n  validateOpts(opts)\n  const optsA = filterObj(opts, isDefined)\n  const {\n    colors: colorsA,\n    stream,\n    ...chalkOpts\n  } = {\n    ...DEFAULT_OPTS,\n    ...optsA,\n  }\n  return { colors: colorsA, stream, chalkOpts }\n}\n\nconst validateOpts = function (opts) {\n  validateBasicOpts(opts)\n  validate(opts, { exampleConfig: EXAMPLE_OPTS, recursiveDenylist: ['stream'] })\n  validateStream(opts)\n}\n\nconst validateBasicOpts = function (opts) {\n  if (!isPlainObj(opts)) {\n    throw new TypeError(`Options must be a plain object: ${opts}`)\n  }\n}\n\nconst validateStream = function ({ stream }) {\n  if (stream !== undefined && !(stream instanceof Stream)) {\n    throw new TypeError(`\"stream\" option must be a stream: ${stream}`)\n  }\n}\n\nconst isDefined = function (key, value) {\n  return value !== undefined\n}\n\nconst DEFAULT_OPTS = {\n  stream: stdout,\n}\n\nconst EXAMPLE_OPTS = {\n  ...DEFAULT_OPTS,\n  colors: true,\n}\n"], "file": "src/options.js"}