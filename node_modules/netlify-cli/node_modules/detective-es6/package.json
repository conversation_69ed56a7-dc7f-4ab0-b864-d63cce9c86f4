{"name": "detective-es6", "version": "4.0.1", "description": "Get the dependencies of an ES6 module", "main": "index.js", "files": ["index.js"], "scripts": {"lint": "xo", "fix": "xo --fix", "mocha": "mocha", "test": "npm run lint && npm run mocha", "test:ci": "c8 npm run mocha"}, "repository": {"type": "git", "url": "git+https://github.com/dependents/node-detective-es6.git"}, "keywords": ["detective", "es6", "es2015", "dependencies", "module", "ast", "import"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dependents/node-detective-es6/issues"}, "homepage": "https://github.com/dependents/node-detective-es6", "engines": {"node": ">=14"}, "dependencies": {"node-source-walk": "^6.0.1"}, "devDependencies": {"c8": "^7.13.0", "mocha": "^10.2.0", "xo": "^0.54.2"}, "xo": {"space": true, "ignores": ["test/fixtures/*"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "curly": ["error", "multi-line"], "operator-linebreak": ["error", "after"], "object-curly-spacing": ["error", "always"], "space-before-function-paren": ["error", "never"], "unicorn/prefer-module": "off", "unicorn/prefer-node-protocol": "off", "unicorn/prefer-top-level-await": "off", "unicorn/prevent-abbreviations": "off"}}}