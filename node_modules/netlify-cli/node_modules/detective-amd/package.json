{"name": "detective-amd", "version": "5.0.2", "description": "Find all dependencies within a JavaScript file using AMD module syntax", "main": "index.js", "bin": {"detective-amd": "bin/cli.js"}, "files": ["bin/cli.js", "index.js"], "scripts": {"lint": "xo", "fix": "xo --fix", "mocha": "mocha", "test": "npm run lint && npm run mocha", "test:ci": "c8 npm run mocha"}, "repository": {"type": "git", "url": "git+https://github.com/dependents/node-detective-amd.git"}, "keywords": ["amd", "detective", "dependencies", "ast", "static analysis", "requirejs", "jsx", "module"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dependents/node-detective-amd/issues"}, "homepage": "https://github.com/dependents/node-detective-amd", "engines": {"node": ">=14"}, "dependencies": {"ast-module-types": "^5.0.0", "escodegen": "^2.0.0", "get-amd-module-type": "^5.0.1", "node-source-walk": "^6.0.1"}, "devDependencies": {"c8": "^7.13.0", "mocha": "^10.2.0", "xo": "^0.54.2"}, "xo": {"space": true, "ignores": ["test/fixtures/*"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "curly": ["error", "multi-line"], "operator-linebreak": ["error", "after"], "object-curly-spacing": ["error", "always"], "prefer-template": "error", "space-before-function-paren": ["error", "never"], "unicorn/explicit-length-check": "off", "unicorn/prefer-module": "off", "unicorn/prefer-node-protocol": "off", "unicorn/prefer-top-level-await": "off", "unicorn/prevent-abbreviations": "off"}}}