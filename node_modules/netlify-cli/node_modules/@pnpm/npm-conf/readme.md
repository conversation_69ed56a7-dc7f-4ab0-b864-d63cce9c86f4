# @pnpm/npm-conf [![Build Status](https://travis-ci.com/pnpm/npm-conf.svg?branch=master)](https://travis-ci.com/pnpm/npm-conf)

> Get the npm config


## Install

```
$ pnpm add @pnpm/npm-conf
```


## Usage

```js
const npmConf = require('@pnpm/npm-conf');

const conf = npmConf();

conf.get('prefix')
//=> //=> /Users/<USER>/.npm-packages

conf.get('registry')
//=> https://registry.npmjs.org/
```

To get a list of all available `npm` config options:

```bash
$ npm config list --long
```


## API

### npmConf()

Returns the `npm` config.

### npmConf.defaults

Returns the default `npm` config.


## License

MIT © [<PERSON>](https://github.com/kevva)
