const Blog = require('../../lib/models/Blog');

exports.handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    await Blog.createTable();
    
    const body = JSON.parse(event.body);
    
    if (!body.title) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'Title is required'
        })
      };
    }

    const blog = new Blog({
      title: body.title,
      slug: body.slug,
      contentJson: body.contentJson,
      coverImageUrl: body.coverImageUrl,
      shortDescription: body.shortDescription,
      tags: body.tags
    });

    const savedBlog = await blog.save();
    
    return {
      statusCode: 201,
      headers,
      body: JSON.stringify({
        success: true,
        data: savedBlog
      })
    };
  } catch (error) {
    console.error('Error creating blog:', error);
    
    if (error.code === '23505') {
      return {
        statusCode: 409,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'Blog with this slug already exists'
        })
      };
    }
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Failed to create blog',
        message: error.message
      })
    };
  }
};
