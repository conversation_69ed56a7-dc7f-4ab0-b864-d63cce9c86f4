const Blog = require('../../lib/models/Blog');

exports.handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'PUT, OPTIONS',
    'Content-Type': 'application/json'
  };

  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  if (event.httpMethod !== 'PUT') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    await Blog.createTable();
    
    const queryParams = event.queryStringParameters || {};
    const { id } = queryParams;

    if (!id) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'ID parameter is required'
        })
      };
    }

    const existingBlog = await Blog.findById(id);
    if (!existingBlog) {
      return {
        statusCode: 404,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'Blog not found'
        })
      };
    }

    const body = JSON.parse(event.body);
    
    existingBlog.title = body.title || existingBlog.title;
    existingBlog.slug = body.slug || existingBlog.generateSlug(existingBlog.title);
    existingBlog.contentJson = body.contentJson !== undefined ? body.contentJson : existingBlog.contentJson;
    existingBlog.coverImageUrl = body.coverImageUrl !== undefined ? body.coverImageUrl : existingBlog.coverImageUrl;
    existingBlog.shortDescription = body.shortDescription !== undefined ? body.shortDescription : existingBlog.shortDescription;
    existingBlog.tags = body.tags !== undefined ? body.tags : existingBlog.tags;
    existingBlog.loves = body.loves !== undefined ? body.loves : existingBlog.loves;
    existingBlog.views = body.views !== undefined ? body.views : existingBlog.views;

    const updatedBlog = await existingBlog.update();
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        data: updatedBlog
      })
    };
  } catch (error) {
    console.error('Error updating blog:', error);
    
    if (error.code === '23505') {
      return {
        statusCode: 409,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'Blog with this slug already exists'
        })
      };
    }
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Failed to update blog',
        message: error.message
      })
    };
  }
};
