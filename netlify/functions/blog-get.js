const Blog = require('../../lib/models/Blog');

exports.handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json'
  };

  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    await Blog.createTable();
    
    const queryParams = event.queryStringParameters || {};
    const { id, slug } = queryParams;

    if (!id && !slug) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'ID or slug parameter is required'
        })
      };
    }

    let blog;
    if (id) {
      blog = await Blog.findById(id);
    } else {
      blog = await Blog.findBySlug(slug);
    }

    if (!blog) {
      return {
        statusCode: 404,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'Blog not found'
        })
      };
    }

    await blog.incrementViews();
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        data: blog
      })
    };
  } catch (error) {
    console.error('Error fetching blog:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Failed to fetch blog',
        message: error.message
      })
    };
  }
};
