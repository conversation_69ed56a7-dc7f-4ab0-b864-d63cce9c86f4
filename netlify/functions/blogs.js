const Blog = require('../../lib/models/Blog');

exports.handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json'
  };

  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    await Blog.createTable();
    
    const queryParams = event.queryStringParameters || {};
    const limit = parseInt(queryParams.limit) || 10;
    const offset = parseInt(queryParams.offset) || 0;

    const blogs = await Blog.findAll(limit, offset);
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        data: blogs,
        pagination: {
          limit,
          offset,
          count: blogs.length
        }
      })
    };
  } catch (error) {
    console.error('Error fetching blogs:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Failed to fetch blogs',
        message: error.message
      })
    };
  }
};
